/**
 * Ultra-Fast Mobile Gallery für extrem flüssiges Swipen
 * Lädt große Batches im Voraus und hält immer genug Bilder bereit
 */

// Globale Variablen
window.totalLoaded = 0;
window.totalItems = 0;
window.isLoading = false;
window.currentIndex = 0;
window.preloadBuffer = 15; // Halte immer 15 Bilder voraus bereit

// Interface State
window.interfaceState = {
    mediaTypeFilter: 'all', // 'all', 'image', 'video'
    creatorFilter: null, // null für alle, oder creator_id
    likedItems: new Set(), // Set von Media-IDs
    bookmarkLists: {}, // Object mit Listen: { "listId": { name: "Name", items: Set() } }
    infoVisible: false,
    currentMediaData: null,
    currentView: 'all', // 'all', 'likes', 'bookmarks', 'list:listId'
    navMenuVisible: false,
    bookmarkModalVisible: false
};

// Ultra-Fast Media-Laden mit großen Batches
async function loadMoreMediaUltraFast(limit = 15) {
    console.log(`🚀 Ultra-fast loading ${limit} items...`);

    if (window.isLoading) {
        console.log('⏳ Already loading...');
        return;
    }

    window.isLoading = true;

    try {
        // Für spezielle Views (Likes, Bookmarks) verwende lokale Daten
        if (window.interfaceState.currentView === 'likes') {
            return loadLikedMedia(limit);
        } else if (window.interfaceState.currentView.startsWith('list:')) {
            const listId = window.interfaceState.currentView.substring(5);
            return loadBookmarkListMedia(listId, limit);
        }

        const offset = window.totalLoaded || 0;
        let url = `/api/media/batch?offset=${offset}&limit=${limit}&fast=true`;

        // Filter hinzufügen
        if (window.interfaceState.mediaTypeFilter !== 'all') {
            url += `&type=${window.interfaceState.mediaTypeFilter}`;
        }
        if (window.interfaceState.creatorFilter) {
            url += `&creator=${window.interfaceState.creatorFilter}`;
        }

        console.log(`🌐 Fetching: ${url}`);

        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        const result = await response.json();
        console.log(`📦 Loaded ${result.data.items.length} items`);

        if (!result.success || !result.data || !result.data.items) {
            throw new Error('Invalid API response');
        }

        const container = document.querySelector('.swipe-container');
        if (!container) {
            throw new Error('Container not found');
        }

        const items = result.data.items;

        // Erstelle alle Media-Elemente parallel
        const promises = items.map((item, index) => {
            return createUltraFastMediaElement(item, window.totalLoaded + index);
        });

        const mediaElements = await Promise.all(promises);

        // Füge alle Elemente gleichzeitig hinzu
        mediaElements.forEach(element => {
            if (element) {
                container.appendChild(element);
            }
        });

        // Update counters
        window.totalLoaded += items.length;
        window.totalItems = result.data.total;

        console.log(`📊 Total: ${window.totalLoaded}/${window.totalItems}`);

        // Zeige erstes Element
        if (window.totalLoaded === items.length && items.length > 0) {
            showUltraFastItem(0);
        }

        // Verstecke Loading-Indikator
        const loadingIndicator = document.querySelector('.loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
        }

    } catch (error) {
        console.error('❌ Error loading media:', error);
    } finally {
        window.isLoading = false;
    }
}

// Ultra-Fast Media-Element-Erstellung
function createUltraFastMediaElement(item, index) {
    return new Promise((resolve) => {
        console.log(`⚡ Creating ultra-fast element ${index}`);

        const element = document.createElement('div');
        element.className = 'swipe-item';
        element.dataset.index = index;
        element.dataset.mediaId = `${item.creator_id}_${item.path}`;
        element.dataset.creatorId = item.creator_id;
        element.dataset.creatorName = item.creator_name;
        element.dataset.mediaType = item.type;
        element.dataset.mediaPath = item.path;
        element.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            display: none;
            align-items: center;
            justify-content: center;
            background: #000;
            opacity: 1;
            transform: translateY(0);
        `;

        // Speichere Metadaten für spätere Verwendung
        element._mediaData = item;

        // Media-Container
        const mediaContainer = document.createElement('div');
        mediaContainer.style.cssText = `
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        `;

        // Minimaler Placeholder
        const placeholder = document.createElement('div');
        placeholder.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            z-index: 10;
        `;
        placeholder.textContent = '⚡';

        // Media-Element
        const mediaEl = document.createElement(item.type === 'video' ? 'video' : 'img');
        mediaEl.style.cssText = `
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        `;

        if (item.type === 'video') {
            mediaEl.controls = true;
            mediaEl.muted = true;
            mediaEl.preload = 'metadata';
        }

        // Bestimme beste URL (Thumbnail zuerst für Geschwindigkeit)
        let imageUrl = item.thumbnail_path ? `/media/${item.thumbnail_path}` : `/media/${item.path}`;

        // Sofortiges Laden ohne Verzögerung
        if (item.type === 'video') {
            mediaEl.src = `/media/${item.path}`;
            mediaEl.onloadedmetadata = () => {
                placeholder.style.display = 'none';
            };
        } else {
            mediaEl.src = imageUrl;
            mediaEl.onload = () => {
                placeholder.style.display = 'none';

                // Lade Vollauflösung im Hintergrund (nur wenn Thumbnail)
                if (item.thumbnail_path && imageUrl.includes('thumbnail')) {
                    setTimeout(() => {
                        const fullImg = new Image();
                        fullImg.onload = () => mediaEl.src = `/media/${item.path}`;
                        fullImg.src = `/media/${item.path}`;
                    }, 100);
                }
            };
        }

        // Minimale Creator-Info
        const info = document.createElement('div');
        info.style.cssText = `
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            background: rgba(0,0,0,0.7);
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            z-index: 20;
        `;
        info.textContent = `${item.creator_name} • ${index + 1}/${window.totalItems || '?'}`;

        mediaContainer.appendChild(placeholder);
        mediaContainer.appendChild(mediaEl);
        mediaContainer.appendChild(info);
        element.appendChild(mediaContainer);

        resolve(element);
    });
}

// Ultra-Fast Item-Anzeige mit flüssigen TikTok-style Animationen
function showUltraFastItem(index, direction = 'next') {
    console.log(`👁️ Showing item ${index} (${direction})`);

    const items = document.querySelectorAll('.swipe-item');
    const currentItem = items[window.currentIndex];
    const nextItem = items[index];

    if (!nextItem) return;

    // Animationsrichtung bestimmen
    const isNext = direction === 'next' || index > window.currentIndex;

    // Aktuelles Item ausblenden
    if (currentItem && currentItem !== nextItem) {
        currentItem.style.transform = isNext ? 'translateY(-100%)' : 'translateY(100%)';
        currentItem.style.opacity = '0';

        setTimeout(() => {
            currentItem.style.display = 'none';
            currentItem.style.transform = 'translateY(0)';
            currentItem.style.opacity = '1';
        }, 300);
    }

    // Nächstes Item vorbereiten und einblenden
    nextItem.style.display = 'flex';
    nextItem.style.transform = isNext ? 'translateY(100%)' : 'translateY(-100%)';
    nextItem.style.opacity = '0';

    // Force reflow für smooth animation
    nextItem.offsetHeight;

    // Einblenden mit Animation
    requestAnimationFrame(() => {
        nextItem.style.transform = 'translateY(0)';
        nextItem.style.opacity = '1';
    });

    window.currentIndex = index;

    // Update Interface mit aktuellen Medien-Daten
    updateInterfaceForCurrentMedia(nextItem);

    // Sehr aggressives Preloading
    const remaining = items.length - index;
    if (remaining <= window.preloadBuffer && window.totalLoaded < window.totalItems && !window.isLoading) {
        console.log(`🚀 Ultra-fast preload: ${remaining} remaining, loading more...`);
        loadMoreMediaUltraFast(20); // Große Batches
    }
}

// Optimierte Touch-Events mit visueller Rückmeldung
function initUltraFastSwipe() {
    const container = document.querySelector('.swipe-container');
    if (!container) return;

    let startY = 0;
    let startTime = 0;
    let isScrolling = false;
    let currentTouchY = 0;
    let isDragging = false;

    container.addEventListener('touchstart', (e) => {
        startY = e.touches[0].clientY;
        currentTouchY = startY;
        startTime = Date.now();
        isScrolling = false;
        isDragging = false;
    }, { passive: true });

    container.addEventListener('touchmove', (e) => {
        currentTouchY = e.touches[0].clientY;
        const deltaY = currentTouchY - startY;
        const absDeltaY = Math.abs(deltaY);

        if (absDeltaY > 10) {
            isScrolling = true;
            isDragging = true;
            e.preventDefault();

            // Visuelle Rückmeldung während des Swipens
            const items = document.querySelectorAll('.swipe-item');
            const currentItem = items[window.currentIndex];

            if (currentItem) {
                // Begrenze die Bewegung für besseres Gefühl
                const maxMove = 100;
                const moveY = Math.max(-maxMove, Math.min(maxMove, deltaY * 0.3));
                const opacity = Math.max(0.7, 1 - (absDeltaY * 0.002));

                currentItem.style.transform = `translateY(${moveY}px)`;
                currentItem.style.opacity = opacity;
                currentItem.style.transition = 'none';
            }
        }
    }, { passive: false });

    container.addEventListener('touchend', (e) => {
        if (!isScrolling) return;

        const endY = e.changedTouches[0].clientY;
        const deltaY = endY - startY;
        const deltaTime = Date.now() - startTime;
        const velocity = Math.abs(deltaY) / deltaTime;

        // Reset current item styling
        const items = document.querySelectorAll('.swipe-item');
        const currentItem = items[window.currentIndex];

        if (currentItem) {
            currentItem.style.transition = 'transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.3s ease';
            currentItem.style.transform = 'translateY(0)';
            currentItem.style.opacity = '1';
        }

        // Verbesserte Swipe-Erkennung mit Geschwindigkeit
        const minDistance = 50;
        const minVelocity = 0.3;

        if ((Math.abs(deltaY) > minDistance || velocity > minVelocity) && deltaTime < 800) {
            if (deltaY > 0 && window.currentIndex > 0) {
                // Swipe nach unten - vorheriges Bild
                setTimeout(() => showUltraFastItem(window.currentIndex - 1, 'prev'), 100);
            } else if (deltaY < 0 && window.currentIndex < items.length - 1) {
                // Swipe nach oben - nächstes Bild
                setTimeout(() => showUltraFastItem(window.currentIndex + 1, 'next'), 100);
            }
        }

        isDragging = false;
    }, { passive: true });
}

// Globale Funktionen
window.loadMoreMedia = loadMoreMediaUltraFast;
window.showItem = showUltraFastItem;
window.createMediaElement = createUltraFastMediaElement;

// Ultra-Fast Initialisierung
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 Ultra-Fast Mobile Gallery initialized');

    // Debug: Prüfe DOM-Elemente
    console.log('🔍 Checking DOM elements...');
    const navMenuBtn = document.getElementById('navMenuBtn');
    const bookmarkBtn = document.getElementById('bookmarkBtn');
    console.log('Nav Menu Button:', navMenuBtn);
    console.log('Bookmark Button:', bookmarkBtn);

    // Lade gespeicherten Zustand
    loadSavedState();

    // Initialisiere Swipe-Events
    initUltraFastSwipe();

    // Initialisiere Interface-Events
    initInterfaceEvents();

    // Lade sofort eine große Menge für flüssiges Swipen
    loadMoreMediaUltraFast(25);

    // Kontinuierliches aggressives Preloading
    setInterval(() => {
        const items = document.querySelectorAll('.swipe-item');
        const remaining = items.length - window.currentIndex;

        if (remaining <= 10 && window.totalLoaded < window.totalItems && !window.isLoading) {
            console.log('🔥 Emergency ultra-fast preload');
            loadMoreMediaUltraFast(20);
        }
    }, 1000); // Prüfe jede Sekunde
});

function initInterfaceEvents() {
    console.log('🎮 Initializing interface events...');

    // Like Button
    const likeBtn = document.getElementById('likeBtn');
    if (likeBtn) {
        likeBtn.addEventListener('click', toggleLike);
        console.log('✅ Like button event listener added');
    } else {
        console.log('❌ Like button not found');
    }

    // Bookmark Button
    const bookmarkBtn = document.getElementById('bookmarkBtn');
    if (bookmarkBtn) {
        bookmarkBtn.addEventListener('click', toggleBookmark);
        console.log('✅ Bookmark button event listener added');
    } else {
        console.log('❌ Bookmark button not found');
    }

    // Info Button
    const infoBtn = document.getElementById('infoBtn');
    if (infoBtn) {
        infoBtn.addEventListener('click', toggleInfo);
        console.log('✅ Info button event listener added');
    } else {
        console.log('❌ Info button not found');
    }

    // Creator Filter Button
    const creatorFilterBtn = document.getElementById('creatorFilterBtn');
    if (creatorFilterBtn) {
        creatorFilterBtn.addEventListener('click', toggleCreatorFilter);
        console.log('✅ Creator filter button event listener added');
    } else {
        console.log('❌ Creator filter button not found');
    }

    // Navigation Menu
    const navMenuBtn = document.getElementById('navMenuBtn');
    if (navMenuBtn) {
        navMenuBtn.addEventListener('click', toggleNavMenu);
        console.log('✅ Navigation menu button event listener added');
    } else {
        console.log('❌ Navigation menu button not found');
    }

    // Navigation Items (initial setup)
    initMainNavigation();

    // Media Filter Buttons (neue Bottom-Filter)
    document.querySelectorAll('.media-filter-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            setMediaTypeFilter(btn.dataset.type);
        });
    });

    // Bookmark Modal Events
    const bookmarkModalCancel = document.getElementById('bookmarkModalCancel');
    if (bookmarkModalCancel) {
        bookmarkModalCancel.addEventListener('click', hideBookmarkModal);
    }

    const bookmarkModalConfirm = document.getElementById('bookmarkModalConfirm');
    if (bookmarkModalConfirm) {
        bookmarkModalConfirm.addEventListener('click', createNewListFromModal);
    }

    const newListInput = document.getElementById('newListInput');
    if (newListInput) {
        newListInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                createNewListFromModal();
            }
        });
    }

    // Info Overlay schließen bei Klick außerhalb
    const infoOverlay = document.getElementById('infoOverlay');
    if (infoOverlay) {
        infoOverlay.addEventListener('click', (e) => {
            if (e.target === infoOverlay) {
                toggleInfo();
            }
        });
    }

    // Bookmark Modal schließen bei Klick außerhalb
    const bookmarkModal = document.getElementById('bookmarkModal');
    if (bookmarkModal) {
        bookmarkModal.addEventListener('click', (e) => {
            if (e.target === bookmarkModal) {
                hideBookmarkModal();
            }
        });
    }

    // Klick außerhalb schließt Navigation
    document.addEventListener('click', (e) => {
        const navMenu = document.querySelector('.nav-menu');
        if (navMenu && !navMenu.contains(e.target) && window.interfaceState.navMenuVisible) {
            hideNavMenu();
        }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        switch(e.key) {
            case 'l':
            case 'L':
                toggleLike();
                break;
            case 'b':
            case 'B':
                toggleBookmark();
                break;
            case 'i':
            case 'I':
                toggleInfo();
                break;
            case 'c':
            case 'C':
                toggleCreatorFilter();
                break;
            case 'm':
            case 'M':
                toggleNavMenu();
                break;
            case 'Escape':
                if (window.interfaceState.bookmarkModalVisible) {
                    hideBookmarkModal();
                } else if (window.interfaceState.infoVisible) {
                    toggleInfo();
                } else if (window.interfaceState.navMenuVisible) {
                    hideNavMenu();
                }
                break;
            case '1':
                setMediaTypeFilter('all');
                break;
            case '2':
                setMediaTypeFilter('image');
                break;
            case '3':
                setMediaTypeFilter('video');
                break;
        }
    });

    console.log('🎮 Interface events initialized');
}

// Interface-Funktionen
function updateInterfaceForCurrentMedia(mediaElement) {
    if (!mediaElement || !mediaElement._mediaData) return;

    const mediaData = mediaElement._mediaData;
    window.interfaceState.currentMediaData = mediaData;

    // Update Creator Info
    const creatorName = document.getElementById('creatorName');
    if (creatorName) {
        creatorName.textContent = window.interfaceState.creatorFilter ?
            `🎯 ${mediaData.creator_name}` :
            mediaData.creator_name;
    }

    // Update Button States
    updateButtonStates(mediaElement.dataset.mediaId);
}

function updateButtonStates(mediaId) {
    // Like Button
    const likeBtn = document.getElementById('likeBtn');
    if (likeBtn) {
        if (window.interfaceState.likedItems.has(mediaId)) {
            likeBtn.classList.add('liked');
        } else {
            likeBtn.classList.remove('liked');
        }
    }

    // Bookmark Button - prüfe ob in irgendeiner Liste
    const bookmarkBtn = document.getElementById('bookmarkBtn');
    if (bookmarkBtn) {
        let isBookmarked = false;
        for (const listData of Object.values(window.interfaceState.bookmarkLists)) {
            if (listData.items.has(mediaId)) {
                isBookmarked = true;
                break;
            }
        }

        if (isBookmarked) {
            bookmarkBtn.classList.add('active');
        } else {
            bookmarkBtn.classList.remove('active');
        }
    }

    // Creator Filter Button
    const creatorFilterBtn = document.getElementById('creatorFilterBtn');
    if (creatorFilterBtn) {
        if (window.interfaceState.creatorFilter) {
            creatorFilterBtn.classList.add('active');
        } else {
            creatorFilterBtn.classList.remove('active');
        }
    }
}

function toggleLike() {
    const currentItem = document.querySelectorAll('.swipe-item')[window.currentIndex];
    if (!currentItem) return;

    const mediaId = currentItem.dataset.mediaId;
    const likeBtn = document.getElementById('likeBtn');

    if (window.interfaceState.likedItems.has(mediaId)) {
        window.interfaceState.likedItems.delete(mediaId);
        likeBtn.classList.remove('liked');
        console.log('❤️ Unliked:', mediaId);
    } else {
        window.interfaceState.likedItems.add(mediaId);
        likeBtn.classList.add('liked');
        console.log('❤️ Liked:', mediaId);
    }

    // Speichere in localStorage
    localStorage.setItem('likedItems', JSON.stringify([...window.interfaceState.likedItems]));
}

function toggleBookmark() {
    const currentItem = document.querySelectorAll('.swipe-item')[window.currentIndex];
    if (!currentItem) return;

    // Zeige Bookmark-Listen-Modal
    showBookmarkModal();
}

function toggleInfo() {
    const infoOverlay = document.getElementById('infoOverlay');
    const infoContent = document.getElementById('infoContent');
    const currentItem = document.querySelectorAll('.swipe-item')[window.currentIndex];

    if (!infoOverlay || !infoContent || !currentItem) return;

    if (window.interfaceState.infoVisible) {
        infoOverlay.classList.remove('visible');
        window.interfaceState.infoVisible = false;
    } else {
        // Lade Info-Daten
        const mediaData = currentItem._mediaData;
        if (mediaData) {
            infoContent.innerHTML = `
                <div class="info-item">
                    <span class="info-label">Creator:</span>
                    <span class="info-value">${mediaData.creator_name}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Typ:</span>
                    <span class="info-value">${mediaData.type === 'video' ? '📹 Video' : '📷 Bild'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Pfad:</span>
                    <span class="info-value">${mediaData.path}</span>
                </div>
                ${mediaData.metadata && Object.keys(mediaData.metadata).length > 0 ?
                    Object.entries(mediaData.metadata).map(([key, value]) =>
                        `<div class="info-item">
                            <span class="info-label">${key}:</span>
                            <span class="info-value">${value}</span>
                        </div>`
                    ).join('') :
                    '<div class="info-item"><span class="info-value">Keine zusätzlichen Metadaten verfügbar</span></div>'
                }
            `;
        }

        infoOverlay.classList.add('visible');
        window.interfaceState.infoVisible = true;
    }
}

function toggleCreatorFilter() {
    const currentItem = document.querySelectorAll('.swipe-item')[window.currentIndex];
    if (!currentItem) return;

    const creatorId = currentItem.dataset.creatorId;
    const creatorFilterBtn = document.getElementById('creatorFilterBtn');

    if (window.interfaceState.creatorFilter === creatorId) {
        // Filter entfernen
        window.interfaceState.creatorFilter = null;
        creatorFilterBtn.classList.remove('active');
        console.log('🎯 Creator filter removed');
    } else {
        // Filter setzen
        window.interfaceState.creatorFilter = creatorId;
        creatorFilterBtn.classList.add('active');
        console.log('🎯 Creator filter set to:', creatorId);
    }

    // Galerie neu laden
    reloadGalleryWithFilters();
}

function setMediaTypeFilter(type) {
    window.interfaceState.mediaTypeFilter = type;

    // Update Button States (neue Bottom-Filter)
    document.querySelectorAll('.media-filter-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.type === type) {
            btn.classList.add('active');
        }
    });

    console.log('📷📹 Media type filter set to:', type);

    // Galerie neu laden
    reloadGalleryWithFilters();
}

function reloadGalleryWithFilters() {
    // Reset state
    window.totalLoaded = 0;
    window.totalItems = 0;
    window.currentIndex = 0;

    // Clear container
    const container = document.querySelector('.swipe-container');
    const existingItems = container.querySelectorAll('.swipe-item');
    existingItems.forEach(item => item.remove());

    // Reload with new filters
    loadMoreMediaUltraFast(25);
}

function loadSavedState() {
    // Lade gespeicherte Likes und Bookmark-Listen
    try {
        const savedLikes = localStorage.getItem('likedItems');
        if (savedLikes) {
            window.interfaceState.likedItems = new Set(JSON.parse(savedLikes));
        }

        const savedBookmarkLists = localStorage.getItem('bookmarkLists');
        if (savedBookmarkLists) {
            const lists = JSON.parse(savedBookmarkLists);
            window.interfaceState.bookmarkLists = {};
            for (const [listId, listData] of Object.entries(lists)) {
                window.interfaceState.bookmarkLists[listId] = {
                    name: listData.name,
                    items: new Set(listData.items || [])
                };
            }
        }

        // Erstelle Standard-Liste falls keine vorhanden
        if (Object.keys(window.interfaceState.bookmarkLists).length === 0) {
            createBookmarkList('Favoriten');
        }
    } catch (e) {
        console.warn('Could not load saved state:', e);
        // Erstelle Standard-Liste bei Fehler
        createBookmarkList('Favoriten');
    }
}

// Bookmark-Listen-System
function createBookmarkList(name) {
    const listId = 'list_' + Date.now();
    window.interfaceState.bookmarkLists[listId] = {
        name: name,
        items: new Set()
    };
    saveBookmarkLists();
    return listId;
}

function saveBookmarkLists() {
    const listsToSave = {};
    for (const [listId, listData] of Object.entries(window.interfaceState.bookmarkLists)) {
        listsToSave[listId] = {
            name: listData.name,
            items: [...listData.items]
        };
    }
    localStorage.setItem('bookmarkLists', JSON.stringify(listsToSave));
}

function addToBookmarkList(listId, mediaId) {
    if (window.interfaceState.bookmarkLists[listId]) {
        window.interfaceState.bookmarkLists[listId].items.add(mediaId);
        saveBookmarkLists();
        console.log(`🔖 Added to list ${listId}:`, mediaId);
        return true;
    }
    return false;
}

function removeFromBookmarkList(listId, mediaId) {
    if (window.interfaceState.bookmarkLists[listId]) {
        window.interfaceState.bookmarkLists[listId].items.delete(mediaId);
        saveBookmarkLists();
        console.log(`🔖 Removed from list ${listId}:`, mediaId);
        return true;
    }
    return false;
}

function isInBookmarkList(listId, mediaId) {
    return window.interfaceState.bookmarkLists[listId]?.items.has(mediaId) || false;
}

function showBookmarkModal() {
    const modal = document.getElementById('bookmarkModal');
    const container = document.getElementById('bookmarkListsContainer');

    // Lade aktuelle Listen
    container.innerHTML = '';

    for (const [listId, listData] of Object.entries(window.interfaceState.bookmarkLists)) {
        const listItem = document.createElement('div');
        listItem.className = 'bookmark-list-item';
        listItem.dataset.listId = listId;

        const currentItem = document.querySelectorAll('.swipe-item')[window.currentIndex];
        const mediaId = currentItem?.dataset.mediaId;
        const isInList = mediaId && isInBookmarkList(listId, mediaId);

        listItem.innerHTML = `
            <div class="bookmark-list-name">${isInList ? '✓ ' : ''}${listData.name}</div>
            <div class="bookmark-list-count">${listData.items.size}</div>
        `;

        listItem.addEventListener('click', () => {
            if (mediaId) {
                if (isInList) {
                    removeFromBookmarkList(listId, mediaId);
                } else {
                    addToBookmarkList(listId, mediaId);
                }
                hideBookmarkModal();
                updateButtonStates(mediaId);
            }
        });

        container.appendChild(listItem);
    }

    modal.classList.add('visible');
    window.interfaceState.bookmarkModalVisible = true;
}

function hideBookmarkModal() {
    const modal = document.getElementById('bookmarkModal');
    const newListInput = document.getElementById('newListInput');
    const confirmBtn = document.getElementById('bookmarkModalConfirm');

    modal.classList.remove('visible');
    newListInput.style.display = 'none';
    newListInput.value = '';
    confirmBtn.style.display = 'none';
    window.interfaceState.bookmarkModalVisible = false;
}

function showCreateListInput() {
    const newListInput = document.getElementById('newListInput');
    const confirmBtn = document.getElementById('bookmarkModalConfirm');

    newListInput.style.display = 'block';
    confirmBtn.style.display = 'block';
    newListInput.focus();
}

function createNewListFromModal() {
    const newListInput = document.getElementById('newListInput');
    const listName = newListInput.value.trim();

    if (listName) {
        const listId = createBookmarkList(listName);

        // Füge aktuelles Medium zur neuen Liste hinzu
        const currentItem = document.querySelectorAll('.swipe-item')[window.currentIndex];
        const mediaId = currentItem?.dataset.mediaId;
        if (mediaId) {
            addToBookmarkList(listId, mediaId);
            updateButtonStates(mediaId);
        }

        hideBookmarkModal();
    }
}

// Navigation und Views
function toggleNavMenu() {
    const dropdown = document.getElementById('navDropdown');
    if (window.interfaceState.navMenuVisible) {
        dropdown.classList.remove('visible');
        window.interfaceState.navMenuVisible = false;
    } else {
        dropdown.classList.add('visible');
        window.interfaceState.navMenuVisible = true;
    }
}

function hideNavMenu() {
    const dropdown = document.getElementById('navDropdown');
    dropdown.classList.remove('visible');
    window.interfaceState.navMenuVisible = false;
}

function switchToView(viewType, listId = null) {
    hideNavMenu();

    const oldView = window.interfaceState.currentView;

    if (viewType === 'all') {
        window.interfaceState.currentView = 'all';
        updateCreatorInfo('Alle Creator');
    } else if (viewType === 'likes') {
        window.interfaceState.currentView = 'likes';
        updateCreatorInfo('❤️ Gelikte Medien');
    } else if (viewType === 'list' && listId) {
        window.interfaceState.currentView = `list:${listId}`;
        const listName = window.interfaceState.bookmarkLists[listId]?.name || 'Unbekannte Liste';
        updateCreatorInfo(`🔖 ${listName}`);
    }

    // Update Navigation
    updateNavigation();

    // Nur neu laden wenn sich die View geändert hat
    if (oldView !== window.interfaceState.currentView) {
        reloadGalleryWithFilters();
    }
}

function updateCreatorInfo(text) {
    const creatorName = document.getElementById('creatorName');
    if (creatorName) {
        creatorName.textContent = text;
    }
}

function updateNavigation() {
    // Update aktive Navigation
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });

    if (window.interfaceState.currentView === 'all') {
        document.getElementById('navHome')?.classList.add('active');
    } else if (window.interfaceState.currentView === 'likes') {
        document.getElementById('navLikes')?.classList.add('active');
    }
}

// Spezielle Loading-Funktionen für Views
async function loadLikedMedia(limit) {
    console.log('❤️ Loading liked media...');

    if (window.interfaceState.likedItems.size === 0) {
        console.log('No liked items found');
        return;
    }

    // Simuliere API-Response für gelikte Medien
    // In einer echten Implementierung würde man die Medien-Details vom Server laden
    const likedArray = [...window.interfaceState.likedItems];
    const batch = likedArray.slice(window.totalLoaded, window.totalLoaded + limit);

    if (batch.length === 0) {
        console.log('No more liked items to load');
        return;
    }

    // Erstelle Mock-Medien-Objekte (in echter App würde man Details vom Server laden)
    const mockItems = batch.map((mediaId, index) => {
        const [creatorId, filename] = mediaId.split('_', 2);
        return {
            path: `cretors/${creatorId}/${filename}`,
            type: filename.toLowerCase().includes('.mp4') ? 'video' : 'image',
            creator_id: creatorId,
            creator_name: creatorId,
            filename: filename,
            metadata: {}
        };
    });

    // Verarbeite wie normale API-Response
    mockItems.forEach((item, index) => {
        const globalIndex = window.totalLoaded + index;
        createUltraFastMediaElement(item, globalIndex);
    });

    window.totalLoaded += batch.length;
    window.totalItems = window.interfaceState.likedItems.size;

    console.log(`❤️ Loaded ${batch.length} liked items`);
}

async function loadBookmarkListMedia(listId, limit) {
    console.log(`🔖 Loading bookmark list: ${listId}`);

    const list = window.interfaceState.bookmarkLists[listId];
    if (!list || list.items.size === 0) {
        console.log('No items in bookmark list');
        return;
    }

    const itemsArray = [...list.items];
    const batch = itemsArray.slice(window.totalLoaded, window.totalLoaded + limit);

    if (batch.length === 0) {
        console.log('No more bookmark items to load');
        return;
    }

    // Erstelle Mock-Medien-Objekte
    const mockItems = batch.map((mediaId, index) => {
        const [creatorId, filename] = mediaId.split('_', 2);
        return {
            path: `cretors/${creatorId}/${filename}`,
            type: filename.toLowerCase().includes('.mp4') ? 'video' : 'image',
            creator_id: creatorId,
            creator_name: creatorId,
            filename: filename,
            metadata: {}
        };
    });

    // Verarbeite wie normale API-Response
    mockItems.forEach((item, index) => {
        const globalIndex = window.totalLoaded + index;
        createUltraFastMediaElement(item, globalIndex);
    });

    window.totalLoaded += batch.length;
    window.totalItems = list.items.size;

    console.log(`🔖 Loaded ${batch.length} bookmark items from list: ${list.name}`);
}

function showBookmarkListsNavigation() {
    hideNavMenu();

    // Erstelle dynamisches Dropdown für Bookmark-Listen
    const dropdown = document.getElementById('navDropdown');
    dropdown.innerHTML = `
        <div class="nav-item" id="navBackToMain">
            <span>←</span>
            <span>Zurück</span>
        </div>
        <div class="nav-divider"></div>
    `;

    // Füge alle Bookmark-Listen hinzu
    for (const [listId, listData] of Object.entries(window.interfaceState.bookmarkLists)) {
        const listItem = document.createElement('div');
        listItem.className = 'nav-item';
        listItem.innerHTML = `
            <span>🔖</span>
            <span>${listData.name} (${listData.items.size})</span>
        `;
        listItem.addEventListener('click', () => {
            switchToView('list', listId);
        });
        dropdown.appendChild(listItem);
    }

    // Zurück-Button
    document.getElementById('navBackToMain').addEventListener('click', () => {
        initMainNavigation();
        hideNavMenu();
    });

    dropdown.classList.add('visible');
    window.interfaceState.navMenuVisible = true;
}

function initMainNavigation() {
    const dropdown = document.getElementById('navDropdown');
    dropdown.innerHTML = `
        <div class="nav-item" id="navHome">
            <span>🏠</span>
            <span>Alle Medien</span>
        </div>
        <div class="nav-divider"></div>
        <div class="nav-item" id="navLikes">
            <span>❤️</span>
            <span>Gelikte Medien</span>
        </div>
        <div class="nav-item" id="navBookmarks">
            <span>🔖</span>
            <span>Bookmark Listen</span>
        </div>
        <div class="nav-divider"></div>
        <div class="nav-item" id="navCreateList">
            <span>➕</span>
            <span>Neue Liste erstellen</span>
        </div>
    `;

    // Event Listener neu hinzufügen
    document.getElementById('navHome').addEventListener('click', () => switchToView('all'));
    document.getElementById('navLikes').addEventListener('click', () => switchToView('likes'));
    document.getElementById('navBookmarks').addEventListener('click', showBookmarkListsNavigation);
    document.getElementById('navCreateList').addEventListener('click', () => {
        hideNavMenu();
        showCreateListInput();
        showBookmarkModal();
    });

    updateNavigation();
}

console.log('⚡ Ultra-Fast Mobile Gallery module loaded');
